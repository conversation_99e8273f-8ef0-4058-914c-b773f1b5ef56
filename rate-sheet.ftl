<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:tblPr>
    <w:tblStyle w:val="TableGrid"/>
    <w:tblW w:w="${styling.table.width!''}" w:type="${styling.table.widthType!''}"/>
    <w:tblBorders>
      <#-- Only render borders if not 'nil' -->
      <#if styling.table.borders.top?? && styling.table.borders.top != "nil">
        <w:top w:val="${styling.table.borders.top!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.left?? && styling.table.borders.left != "nil">
        <w:left w:val="${styling.table.borders.left!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.bottom?? && styling.table.borders.bottom != "nil">
        <w:bottom w:val="${styling.table.borders.bottom!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.right?? && styling.table.borders.right != "nil">
        <w:right w:val="${styling.table.borders.right!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.insideH?? && styling.table.borders.insideH != "nil">
        <w:insideH w:val="${styling.table.borders.insideH!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.insideV?? && styling.table.borders.insideV != "nil">
        <w:insideV w:val="${styling.table.borders.insideV!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
    </w:tblBorders>
  </w:tblPr>

  <#-- Handle array data structure from SQL functions -->
  <#assign dataObj = .data_model>
  <#if dataObj?is_sequence && dataObj?has_content>
    <#assign dataObj = dataObj[0]>
  </#if>

  <#-- Extract sections, carriers, calculations, and classLegend from data object -->
  <#assign sections = dataObj.sections![]>
  <#assign carriers = dataObj.carriers![]>
  <#assign calculations = dataObj.calculations![]>
  <#assign classLegend = dataObj.classLegend!"">

  <#-- Calculate total number of sections for dynamic sizing -->
  <#assign totalSections = 0>
  <#if sections??>
    <#assign totalSections = sections?size>
  </#if>
  <#if calculations?? && calculations?has_content>
    <#assign totalSections = totalSections + calculations?size>
  </#if>

  <#-- Determine dynamic sizing based on total sections -->
  <#if totalSections gt 12>
    <#-- Reduced sizing for tables with many sections -->
    <#assign dynamicHeaderHeight = (styling.rows.header.height?number * 0.95)?round>
    <#assign dynamicSubheaderHeight = (styling.rows.subheader.height?number * 0.95)?round>
    <#assign dynamicBenefitHeight = (styling.rows.benefit.height?number * 0.85)?round>
    <#assign dynamicCellMaxWidth = (styling.rows.custom.cellMaxWidth?number * 0.95)?round>
    <#assign dynamicCellMinWidth = (styling.rows.custom.cellMinWidth?number * 0.95)?round>
    <#assign dynamicHeaderFontSize = (styling.rows.header.text.fontSize?number * 0.95)?round>
    <#assign dynamicSubheaderFontSize = (styling.rows.subheader.text.fontSize?number * 0.95)?round>
    <#assign dynamicBenefitFontSize = (styling.rows.benefit.benefitText.fontSize?number * 0.95)?round>
    <#assign dynamicCarrierFontSize = (styling.rows.benefit.carrierText.fontSize?number * 0.95)?round>
    <#assign dynamicClassWidth = 1440>
  <#else>
    <#-- Normal sizing for tables with fewer sections -->
    <#assign dynamicHeaderHeight = styling.rows.header.height>
    <#assign dynamicSubheaderHeight = styling.rows.subheader.height>
    <#assign dynamicBenefitHeight = styling.rows.benefit.height>
    <#assign dynamicCellMaxWidth = styling.rows.custom.cellMaxWidth!'5000'>
    <#assign dynamicCellMinWidth = styling.rows.custom.cellMinWidth!'2000'>
    <#assign dynamicHeaderFontSize = styling.rows.header.text.fontSize>
    <#assign dynamicSubheaderFontSize = styling.rows.subheader.text.fontSize>
    <#assign dynamicBenefitFontSize = styling.rows.benefit.benefitText.fontSize>
    <#assign dynamicCarrierFontSize = styling.rows.benefit.carrierText.fontSize>
    <#assign dynamicClassWidth = 1800>
  </#if>

  <w:tblGrid>
    <#-- Check if any section has className to determine if we need the class column -->
    <#assign hasClassName = false>
    <#if sections?? && sections?has_content>
      <#list sections as section>
        <#if section.className??>
          <#assign hasClassName = true>
          <#break>
        </#if>
      </#list>
    </#if>

    <#if hasClassName>
      <w:gridCol w:w="${dynamicClassWidth}"/>
    </#if>
    <w:gridCol w:w="${dynamicCellMaxWidth}"/>
    <w:gridCol w:w="${dynamicCellMinWidth}"/>
    <#if carriers??>
      <#list carriers as carrier>
        <w:gridCol w:w="${dynamicCellMinWidth}"/>
        <w:gridCol w:w="${dynamicCellMinWidth}"/>
      </#list>
    </#if>
  </w:tblGrid>

  <!-- Header row -->
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${dynamicHeaderHeight!''}"/>
    </w:trPr>

    <!-- Class header column (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
          <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor!''}"/>
          <w:vAlign w:val="${styling.rows.header.cell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.header.text.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.header.text.color!''}"/>
              <w:sz w:val="${dynamicHeaderFontSize!''}"/>
              <#if styling.rows.header.text.bold!false><w:b/></#if>
            </w:rPr>
            <w:t>Class</w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMaxWidth}" w:type="dxa"/>
        <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor!''}"/>
        <w:vAlign w:val="${styling.rows.header.cell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.header.text.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.header.text.color!''}"/>
            <w:sz w:val="${dynamicHeaderFontSize!''}"/>
            <#if styling.rows.header.text.bold!false><w:b/></#if>
          </w:rPr>
          <w:t>Benefit</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Volume header column -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
        <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor!''}"/>
        <w:vAlign w:val="${styling.rows.header.cell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.header.text.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.header.text.color!''}"/>
            <w:sz w:val="${dynamicHeaderFontSize!''}"/>
            <#if styling.rows.header.text.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if carriers??>
      <#list carriers as carrier>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor!''}"/>
            <w:vAlign w:val="${styling.rows.header.cell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.header.text.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.header.text.color!''}"/>
                <w:sz w:val="${dynamicHeaderFontSize!''}"/>
                <#if styling.rows.header.text.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${carrier}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>

  <!-- Subheader row -->
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${dynamicSubheaderHeight!''}"/>
    </w:trPr>

    <!-- Empty class subheader (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
          <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.subheader.cell.backgroundColor!''}"/>
          <w:vAlign w:val="${styling.rows.subheader.cell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.subheader.text.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.subheader.text.color!''}"/>
              <w:sz w:val="${dynamicSubheaderFontSize!''}"/>
              <#if styling.rows.subheader.text.bold!false><w:b/></#if>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMaxWidth}" w:type="dxa"/>
        <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.subheader.cell.backgroundColor!''}"/>
        <w:vAlign w:val="${styling.rows.subheader.cell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.subheader.text.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.subheader.text.color!''}"/>
            <w:sz w:val="${dynamicSubheaderFontSize!''}"/>
            <#if styling.rows.subheader.text.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume subheader -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
        <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.subheader.cell.backgroundColor!''}"/>
        <w:vAlign w:val="${styling.rows.subheader.cell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.subheader.text.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.subheader.text.color!''}"/>
            <w:sz w:val="${dynamicSubheaderFontSize!''}"/>
            <#if styling.rows.subheader.text.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if carriers??>
      <#list carriers as carrier>
        <#list ["Rates", "Premium"] as label>
          <w:tc>
            <w:tcPr>
              <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
              <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.subheader.cell.backgroundColor!''}"/>
              <w:vAlign w:val="${styling.rows.subheader.cell.verticalAlign!''}"/>
            </w:tcPr>
            <w:p>
              <w:pPr>
                <w:jc w:val="${styling.rows.subheader.text.alignment!''}"/>
              </w:pPr>
              <w:r>
                <w:rPr>
                  <w:color w:val="${styling.rows.subheader.text.color!''}"/>
                  <w:sz w:val="${dynamicSubheaderFontSize!''}"/>
                  <#if styling.rows.subheader.text.bold!false><w:b/></#if>
                </w:rPr>
                <w:t>${label}</w:t>
              </w:r>
            </w:p>
          </w:tc>
        </#list>
      </#list>
    </#if>
  </w:tr>

  <!-- Benefit rows -->
  <#if sections??>
    <#list sections as section>
      <w:tr>
        <w:trPr>
          <w:trHeight w:val="${dynamicBenefitHeight!''}"/>
        </w:trPr>

        <!-- Class name cell (conditional) -->
        <#if hasClassName>
          <w:tc>
            <w:tcPr>
              <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
              <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
            </w:tcPr>
            <w:p>
              <w:pPr>
                <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
              </w:pPr>
              <w:r>
                <w:rPr>
                  <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
                  <w:sz w:val="${dynamicBenefitFontSize!''}"/>
                  <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
                </w:rPr>
                <w:t>${section.className!''}</w:t>
              </w:r>
            </w:p>
          </w:tc>
        </#if>

        <w:tc>
          <w:tcPr>
            <w:tcW w:w="${dynamicCellMaxWidth}" w:type="dxa"/>
            <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
                <w:sz w:val="${dynamicBenefitFontSize!''}"/>
                <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${section.name}</w:t>
            </w:r>
          </w:p>
        </w:tc>

        <!-- Volume column -->
        <w:tc>
          <w:tcPr>
            <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:sz w:val="${dynamicCarrierFontSize!''}"/>
                <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>
                <#if carriers??>
                  <#list carriers as carrier>
                    <#assign val = section.values[carrier]!{}>
                    <#if val.volume?? && val.volume?has_content && val.volume?is_number>
                      ${val.volume}
                      <#break>
                    </#if>
                  </#list>
                </#if>
              </w:t>
            </w:r>
          </w:p>
        </w:tc>

        <#if carriers??>
          <#list carriers as carrier>
            <#assign val = section.values[carrier]!{}>
            <#list ["rate", "premium"] as key>
              <w:tc>
                <w:tcPr>
                  <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
                  <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
                </w:tcPr>
                <w:p>
                  <w:pPr>
                    <w:jc w:val="right"/>
                  </w:pPr>
                  <w:r>
                    <w:rPr>
                      <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                      <w:sz w:val="${dynamicCarrierFontSize!''}"/>
                      <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
                    </w:rPr>
                    <w:t>${val[key]!''}</w:t>
                  </w:r>
                </w:p>
              </w:tc>
            </#list>
          </#list>
        </#if>
      </w:tr>
    </#list>
  </#if>
    <!-- Empty spacing row -->
  <w:tr>
    <!-- Class column spacing (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
          <w:vAlign w:val="center"/>
        </w:tcPr>
        <w:p>
          <w:pPr><w:spacing w:after="120"/></w:pPr>
          <w:r><w:t></w:t></w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMaxWidth}" w:type="dxa"/>
        <w:vAlign w:val="center"/>
      </w:tcPr>
      <w:p>
        <w:pPr><w:spacing w:after="120"/></w:pPr>
        <w:r><w:t></w:t></w:r>
      </w:p>
    </w:tc>

    <!-- Volume column spacing -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
        <w:vAlign w:val="center"/>
      </w:tcPr>
      <w:p>
        <w:pPr><w:spacing w:after="120"/></w:pPr>
        <w:r><w:t></w:t></w:r>
      </w:p>
    </w:tc>

    <#if carriers??>
      <#list carriers as carrier>
        <#list 1..2 as x>
          <w:tc>
            <w:tcPr>
              <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
              <w:vAlign w:val="center"/>
            </w:tcPr>
            <w:p>
              <w:pPr><w:spacing w:after="120"/></w:pPr>
              <w:r><w:t></w:t></w:r>
            </w:p>
          </w:tc>
        </#list>
      </#list>
    </#if>
  </w:tr>

  <!-- Total Monthly Premiums row (conditional) -->
  <#if calculations?? && calculations?has_content>
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${dynamicBenefitHeight!''}"/>
    </w:trPr>

    <!-- Empty class cell for totals (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:b/>
              <w:sz w:val="${dynamicBenefitFontSize!''}"/>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMaxWidth}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
            <w:b/>
            <w:sz w:val="${dynamicBenefitFontSize!''}"/>
          </w:rPr>
          <w:t>Total Monthly Premiums</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume cell for totals -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
            <w:b/>
            <w:sz w:val="${dynamicCarrierFontSize!''}"/>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if calculations??>
      <#list calculations as calc>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:b/>
                <w:sz w:val="${dynamicCarrierFontSize!''}"/>
              </w:rPr>
              <w:t>${calc.totalMonthlyPremiums!''}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>
  </#if>

  <!-- Annual Premium row (conditional) -->
  <#if calculations?? && calculations?has_content>
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${dynamicBenefitHeight!''}"/>
    </w:trPr>

    <!-- Empty class cell (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${dynamicBenefitFontSize!''}"/>
              <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMaxWidth}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
            <w:sz w:val="${dynamicBenefitFontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t>Annual Premium</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume cell -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
            <w:sz w:val="${dynamicCarrierFontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if calculations??>
      <#list calculations as calc>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:sz w:val="${dynamicCarrierFontSize!''}"/>
                <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${calc.annualPremium!''}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>
  </#if>

  <!-- $ Difference From #1 row (conditional) -->
  <#if calculations?? && calculations?has_content && calculations?filter(calc -> calc["$ Difference From #1"]??)?has_content>
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${dynamicBenefitHeight!''}"/>
    </w:trPr>

    <!-- Empty class cell (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${dynamicBenefitFontSize!''}"/>
              <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMaxWidth}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
            <w:sz w:val="${dynamicBenefitFontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t>$ Difference From #1</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume cell -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
            <w:sz w:val="${dynamicCarrierFontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if calculations??>
      <#list calculations as calc>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:sz w:val="${dynamicCarrierFontSize!''}"/>
                <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${calc["$ Difference From #1"]!''}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>
  </#if>

  <!-- Percentage Different From #1 row (conditional) -->
  <#if calculations?? && calculations?has_content && calculations?filter(calc -> calc["Percentage Different From #1"]??)?has_content>
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="${dynamicBenefitHeight!''}"/>
    </w:trPr>

    <!-- Empty class cell (conditional) -->
    <#if hasClassName>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${dynamicBenefitFontSize!''}"/>
              <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
            </w:rPr>
            <w:t></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#if>

    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMaxWidth}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
            <w:sz w:val="${dynamicBenefitFontSize!''}"/>
            <#if styling.rows.benefit.benefitText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t>Percentage Different From #1</w:t>
        </w:r>
      </w:p>
    </w:tc>

    <!-- Empty volume cell -->
    <w:tc>
      <w:tcPr>
        <w:tcW w:w="${dynamicCellMinWidth}" w:type="dxa"/>
        <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
      </w:tcPr>
      <w:p>
        <w:pPr>
          <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
            <w:sz w:val="${dynamicCarrierFontSize!''}"/>
            <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
          </w:rPr>
          <w:t></w:t>
        </w:r>
      </w:p>
    </w:tc>

    <#if calculations??>
      <#list calculations as calc>
        <w:tc>
          <w:tcPr>
            <w:gridSpan w:val="2"/>
            <w:vAlign w:val="${styling.rows.benefit.carrierCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.carrierText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.carrierText.color!''}"/>
                <w:sz w:val="${dynamicCarrierFontSize!''}"/>
                <#if styling.rows.benefit.carrierText.bold!false><w:b/></#if>
              </w:rPr>
              <w:t>${calc["Percentage Different From #1"]!''}</w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#list>
    </#if>
  </w:tr>
  </#if>

  <!-- Class Legend row (conditional) -->
  <#if classLegend?? && classLegend?has_content>
    <w:tr>
      <w:trPr>
        <w:trHeight w:val="${dynamicBenefitHeight!''}"/>
      </w:trPr>

      <!-- Empty class cell (conditional) -->
      <#if hasClassName>
        <w:tc>
          <w:tcPr>
            <w:tcW w:w="${dynamicClassWidth}" w:type="dxa"/>
            <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
                <w:sz w:val="${dynamicBenefitFontSize!''}"/>
              </w:rPr>
              <w:t></w:t>
            </w:r>
          </w:p>
        </w:tc>
      </#if>

      <w:tc>
        <w:tcPr>
          <w:gridSpan w:val="${(carriers?size * 2 + 2)!''}"/>
          <w:vAlign w:val="${styling.rows.benefit.benefitCell.verticalAlign!''}"/>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="${styling.rows.benefit.benefitText.alignment!''}"/>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
               <w:i/>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${dynamicBenefitFontSize!''}"/>
            </w:rPr>
            <w:t>Class Legend : </w:t>
          </w:r>
          <w:r>
            <w:rPr>
             <w:i/>
              <w:color w:val="${styling.rows.benefit.benefitText.color!''}"/>
              <w:sz w:val="${dynamicBenefitFontSize!''}"/>
            </w:rPr>
            <w:t><#if classLegend?is_sequence>${classLegend?join(", ")}<#else>${classLegend}</#if></w:t>
          </w:r>
        </w:p>
      </w:tc>
    </w:tr>
  </#if>
</w:tbl>